\relax 
\citation{ref1}
\citation{ref2}
\citation{ref3}
\Newlabel{label1}{a}
\Newlabel{label2}{b}
\@writefile{toc}{\contentsline {section}{\numberline {1}Introduction}{1}{}\protected@file@percent }
\newlabel{sec1}{{1}{1}{}{}{}}
\citation{ref4}
\citation{ref5}
\citation{ref6}
\citation{ref7}
\citation{ref8}
\citation{ref9}
\citation{ref10}
\citation{ref11}
\citation{ref12}
\citation{ref13}
\citation{ref14}
\citation{ref15}
\citation{ref16}
\citation{ref17}
\citation{ref18}
\citation{ref19}
\citation{ref20}
\citation{ref21}
\citation{ref22}
\citation{ref23}
\citation{ref24}
\citation{ref25}
\citation{ref26}
\citation{ref27}
\citation{ref28}
\citation{ref29}
\@writefile{toc}{\contentsline {section}{\numberline {2}Methods}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}Overview}{4}{}\protected@file@percent }
\newlabel{subsec1}{{2.1}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}Module Design}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.2.1}Deep Network}{4}{}\protected@file@percent }
\newlabel{eq1}{{1}{4}{}{}{}}
\newlabel{eq2}{{2}{4}{}{}{}}
\newlabel{eq3}{{3}{5}{}{}{}}
\newlabel{eq4}{{4}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.2.2}Dynamic Bayesian Network}{5}{}\protected@file@percent }
\newlabel{eq5}{{5}{5}{}{}{}}
\newlabel{eq6}{{6}{5}{}{}{}}
\newlabel{eq7}{{7}{6}{}{}{}}
\newlabel{eq8}{{8}{6}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.3}System Solution and Robustness Analysis}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.3.1}Mathematics}{7}{}\protected@file@percent }
\citation{ref1}
\bibcite{ref1}{{1}{}{{}}{{}}}
\bibcite{ref2}{{2}{}{{}}{{}}}
\bibcite{ref3}{{3}{}{{}}{{}}}
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces Table Caption}}{8}{}\protected@file@percent }
\newlabel{tab1}{{1}{8}{}{}{}}
\@writefile{toc}{\let\numberline\tmptocnumberline}
\@writefile{toc}{\contentsline {section}{\numberline {Appendix ~A}Example Appendix Section}{8}{}\protected@file@percent }
\newlabel{app1}{{Appendix ~A}{8}{}{}{}}
\bibcite{ref4}{{4}{}{{}}{{}}}
\bibcite{ref5}{{5}{}{{}}{{}}}
\bibcite{ref6}{{6}{}{{}}{{}}}
\bibcite{ref7}{{7}{}{{}}{{}}}
\bibcite{ref8}{{8}{}{{}}{{}}}
\bibcite{ref9}{{9}{}{{}}{{}}}
\bibcite{ref10}{{10}{}{{}}{{}}}
\bibcite{ref11}{{11}{}{{}}{{}}}
\bibcite{ref12}{{12}{}{{}}{{}}}
\bibcite{ref13}{{13}{}{{}}{{}}}
\bibcite{ref14}{{14}{}{{}}{{}}}
\bibcite{ref15}{{15}{}{{}}{{}}}
\bibcite{ref16}{{16}{}{{}}{{}}}
\bibcite{ref17}{{17}{}{{}}{{}}}
\bibcite{ref18}{{18}{}{{}}{{}}}
\bibcite{ref19}{{19}{}{{}}{{}}}
\bibcite{ref20}{{20}{}{{}}{{}}}
\bibcite{ref21}{{21}{}{{}}{{}}}
\bibcite{ref22}{{22}{}{{}}{{}}}
\bibcite{ref23}{{23}{}{{}}{{}}}
\bibcite{ref24}{{24}{}{{}}{{}}}
\bibcite{ref25}{{25}{}{{}}{{}}}
\bibcite{ref26}{{26}{}{{}}{{}}}
\bibcite{ref27}{{27}{}{{}}{{}}}
\bibcite{ref28}{{28}{}{{}}{{}}}
\bibcite{ref29}{{29}{}{{}}{{}}}
\providecommand\NAT@force@numbers{}\NAT@force@numbers
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces The proposed DNN-PGM SLG framework ($Phi$KFM). (a) The training of $Phi$KFM. In the part of DNN, includes 3 modules: the encoder $(f_e)$, the attention module $(f_{att})$, the decoder $(f_d)$. In the part of PGM, we use a KFM, which is a probability graph model, to optimize the initial generated skeleton. The system can generate optimal skeleton sequence $\mathbf  {X}$ obeyed the real-world physical motion laws. The loss function $\mathcal  {L}$ is the combination of KL distance $(\mathcal  {L}_{KL})$ and physical constraints $(\mathcal  {L}_{phy})$ given by inputted control $\mathbf  {U}$. (b) The testing of model. To build a SL discriminator for testing the classification accuracy of generated SL.}}{12}{}\protected@file@percent }
\newlabel{fig1}{{1}{12}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces The physical constraint of SL in time $t$.}}{13}{}\protected@file@percent }
\newlabel{fig2}{{2}{13}{}{}{}}
\gdef \@abspage@last{15}
