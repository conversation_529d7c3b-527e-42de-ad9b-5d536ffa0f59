This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.8.28)  19 JUN 2025 17:41
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/Desktop/Elsarticle/elsarticle/elsarticle-template-num.tex
(c:/Users/<USER>/Desktop/Elsarticle/elsarticle/elsarticle-template-num.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(d:/texlive/2024/texmf-dist/tex/latex/elsarticle/elsarticle.cls
Document Class: elsarticle 2020/11/20, 3.3: Elsevier Ltd
(d:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (d:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count188
\l__pdf_internal_box=\box51
)) (d:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
) (d:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count189
)
\@bls=\dimen140
 (d:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(d:/texlive/2024/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count190
\c@section=\count191
\c@subsection=\count192
\c@subsubsection=\count193
\c@paragraph=\count194
\c@subparagraph=\count195
\c@figure=\count196
\c@table=\count197
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen141
) (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen142
\Gin@req@width=\dimen143
)
\c@tnote=\count198
\c@fnote=\count199
\c@cnote=\count266
\c@ead=\count267
\c@author=\count268
\@eadauthor=\toks18
\c@affn=\count269
\absbox=\box52
\elsarticlehighlightsbox=\box53
\elsarticlegrabsbox=\box54
\keybox=\box55
\Columnwidth=\dimen144
\space@left=\dimen145
\els@boxa=\box56
\els@boxb=\box57
\leftMargin=\dimen146
\@enLab=\toks19
\@sep=\skip50
\@@sep=\skip51
 (./elsarticle-template-num.spl) (d:/texlive/2024/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip52
\bibsep=\skip53
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count270
)
\splwrite=\write3
\openout3 = `elsarticle-template-num.spl'.

\appnamewidth=\dimen147
) (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks20
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip54

For additional information on amsmath, use the `?' option.
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen148
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen149
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box58
\strutbox@=\box59
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen150
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip16
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks22
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks23
\eqnshift@=\dimen151
\alignsep@=\dimen152
\tagshift@=\dimen153
\tagwidth@=\dimen154
\totwidth@=\dimen155
\lineht@=\dimen156
\@envbody=\toks24
\multlinegap=\skip55
\multlinetaggap=\skip56
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (./elsarticle-template-num.aux)
\openout1 = `elsarticle-template-num.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 54.
LaTeX Font Info:    ... okay on input line 54.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 54.
LaTeX Font Info:    ... okay on input line 54.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 54.
LaTeX Font Info:    ... okay on input line 54.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 54.
LaTeX Font Info:    ... okay on input line 54.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 54.
LaTeX Font Info:    ... okay on input line 54.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 54.
LaTeX Font Info:    ... okay on input line 54.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 54.
LaTeX Font Info:    ... okay on input line 54.
 (d:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count283
\scratchdimen=\dimen157
\scratchbox=\box60
\nofMPsegments=\count284
\nofMParguments=\count285
\everyMPshowfont=\toks26
\MPscratchCnt=\count286
\MPscratchDim=\dimen158
\MPnumerator=\count287
\makeMPintoPDFobject=\count288
\everyMPtoPDFconversion=\toks27
) (d:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
 (d:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)) (d:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)) (d:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPEG,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.
 (d:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
LaTeX Font Info:    Trying to load font information for U+msa on input line 124.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 124.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Overfull \hbox (13.71628pt too wide) in paragraph at lines 123--125
\OT1/cmr/m/n/12 tiotem-po-ral co-her-ence. $^^H$KFM ex-plic-itly in-te-grates phys-i-cal con-straints through
 []


Overfull \hbox (33.83743pt too wide) in paragraph at lines 128--128
$\OT1/cmr/m/n/12 ^^H$\OT1/cmr/bx/n/12 KFM: Physically-Constrained Sign Lan-guage Gen-er-a-tion via Attention-
 []


Overfull \hbox (33.83743pt too wide) in paragraph at lines 133--133
$\OT1/cmr/m/n/12 ^^H$\OT1/cmr/bx/n/12 KFM: Physically-Constrained Sign Lan-guage Gen-er-a-tion via Attention-
 []

[1

{d:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}] [2

{d:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}]

Package natbib Warning: Citation `ref1' on page 1 undefined on input line 158.


Package natbib Warning: Citation `ref2' on page 1 undefined on input line 158.


Package natbib Warning: Citation `ref3' on page 1 undefined on input line 158.


Overfull \hbox (2.61108pt too wide) has occurred while \output is active
[] 
 []

[1


]

Package natbib Warning: Citation `ref4' on page 2 undefined on input line 160.


Package natbib Warning: Citation `ref5' on page 2 undefined on input line 160.


Package natbib Warning: Citation `ref6' on page 2 undefined on input line 160.


Package natbib Warning: Citation `ref7' on page 2 undefined on input line 160.


Package natbib Warning: Citation `ref8' on page 2 undefined on input line 160.


Package natbib Warning: Citation `ref9' on page 2 undefined on input line 160.


Package natbib Warning: Citation `ref10' on page 2 undefined on input line 162.


Package natbib Warning: Citation `ref11' on page 2 undefined on input line 162.


Package natbib Warning: Citation `ref12' on page 2 undefined on input line 162.


Package natbib Warning: Citation `ref13' on page 2 undefined on input line 162.


Package natbib Warning: Citation `ref14' on page 2 undefined on input line 162.


Package natbib Warning: Citation `ref15' on page 2 undefined on input line 162.


Package natbib Warning: Citation `ref16' on page 2 undefined on input line 162.


Package natbib Warning: Citation `ref17' on page 2 undefined on input line 162.


Package natbib Warning: Citation `ref18' on page 2 undefined on input line 164.


Package natbib Warning: Citation `ref19' on page 2 undefined on input line 164.


Package natbib Warning: Citation `ref20' on page 2 undefined on input line 164.


Package natbib Warning: Citation `ref21' on page 2 undefined on input line 164.


Package natbib Warning: Citation `ref22' on page 2 undefined on input line 164.


Package natbib Warning: Citation `ref23' on page 2 undefined on input line 164.


Overfull \hbox (0.29178pt too wide) in paragraph at lines 164--165
\OT1/cmr/m/n/12 tem-po-ral co-or-di-na-tion and smooth mo-tion tran-si-tions|remains a ma-jor chal-
 []


Package natbib Warning: Citation `ref24' on page 2 undefined on input line 166.


Package natbib Warning: Citation `ref25' on page 2 undefined on input line 166.


Package natbib Warning: Citation `ref26' on page 2 undefined on input line 166.


Package natbib Warning: Citation `ref27' on page 2 undefined on input line 166.


Package natbib Warning: Citation `ref28' on page 2 undefined on input line 166.


Package natbib Warning: Citation `ref29' on page 2 undefined on input line 166.

[2] [3]

LaTeX Warning: Reference `fig1' on page 4 undefined on input line 182.



pdfTeX warning: pdflatex.exe (file ./Figure_1.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_1.pdf, id=26, 559.73117pt x 668.97928pt>
File: Figure_1.pdf Graphic file (type pdf)
<use Figure_1.pdf>
Package pdftex.def Info: Figure_1.pdf  used on input line 186.
(pdftex.def)             Requested size: 390.0pt x 466.12846pt.

LaTeX Warning: Float too large for page by 25.5729pt on input line 188.


LaTeX Warning: Reference `fig1' on page 4 undefined on input line 192.

[4]

LaTeX Warning: Reference `fig2' on page 5 undefined on input line 226.

[5]

LaTeX Warning: Reference `eq5' on page 6 undefined on input line 243.



pdfTeX warning: pdflatex.exe (file ./Figure_2.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_2.pdf, id=40, 356.65245pt x 192.9609pt>
File: Figure_2.pdf Graphic file (type pdf)
<use Figure_2.pdf>
Package pdftex.def Info: Figure_2.pdf  used on input line 262.
(pdftex.def)             Requested size: 390.0pt x 211.01807pt.
c:/Users/<USER>/Desktop/Elsarticle/elsarticle/elsarticle-template-num.tex:266: Missing $ inserted.
<inserted text> 
                $
l.266 p\left
            (\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t+1},u_{1:t+1}\right)
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

c:/Users/<USER>/Desktop/Elsarticle/elsarticle/elsarticle-template-num.tex:284: Undefined control sequence.
l.284 \max\below
                {\mathbf{x}_{1:T-1}}{p\left(\mathbf{x}_{1:T}\middle|{\hat{k}...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

c:/Users/<USER>/Desktop/Elsarticle/elsarticle/elsarticle-template-num.tex:285: Undefined control sequence.
l.285 ...{{\hat{k}}_T,u_T|\mathbf{x}}_T)\max\below
                                                  {\mathbf{x}_{T-1}}{p\left(...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

c:/Users/<USER>/Desktop/Elsarticle/elsarticle/elsarticle-template-num.tex:285: Undefined control sequence.
l.285 ...\middle|\mathbf{x}_{T-1}\right)\max\below
                                                  {\mathbf{x}_{1:T-2}}{p\lef...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

c:/Users/<USER>/Desktop/Elsarticle/elsarticle/elsarticle-template-num.tex:295: Missing $ inserted.
<inserted text> 
                $
l.295 
      
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.


Overfull \hbox (733.6612pt too wide) in paragraph at lines 266--295
[]\OT1/cmr/m/n/12 p$[] = \OML/cmm/m/it/12 p [] p [] \OT1/cmr/m/n/12 = \OML/cmm/m/it/12 p [] [] []:\OT1/cmr/m/n/12 (10)\OML/cmm/m/it/12 Thenext; basedonthefilteringfunction; wecangetsmoothfunctionofgraphmodel:Forattimej\OT1/cmr/m/n/12 (1 \OML/cmm/m/it/12 <
 []


Overfull \hbox (208.45996pt too wide) in paragraph at lines 266--295
\OML/cmm/m/it/12 j < t\OT1/cmr/m/n/12 )\OML/cmm/m/it/12 ; wehave \OT1/cmr/m/n/12 : \OML/cmm/m/it/12 p [] \OT1/cmr/m/n/12 = \OML/cmm/m/it/12 p [] \OT1/cmr/m/n/12 = \OML/cmm/m/it/12 p [] p [] \OT1/cmr/m/n/12 =
 []


Overfull \hbox (375.46089pt too wide) in paragraph at lines 266--295
\OML/cmm/m/it/12 p [] p [] ; \OT1/cmr/m/n/12 (11)\OML/cmm/m/it/12 wherep [] isfilteringpart; whichcanbeprocessedbyeq:\OT1/cmr/m/n/12 (5)\OML/cmm/m/it/12 ; and p [] iscalculatedby \OT1/cmr/m/n/12 :
 []


Overfull \hbox (173.72098pt too wide) in paragraph at lines 266--295
\OML/cmm/m/it/12 p [] \OT1/cmr/m/n/12 = [] []\OML/cmm/m/it/12 d\OT1/cmr/bx/n/12 x[] \OT1/cmr/m/n/12 = [] []\OML/cmm/m/it/12 d\OT1/cmr/bx/n/12 x[] \OT1/cmr/m/n/12 =
 []


Overfull \hbox (293.99852pt too wide) in paragraph at lines 266--295
[] [] \OT1/cmr/m/n/12 = [] []\OML/cmm/m/it/12 :\OT1/cmr/m/n/12 (12)\OML/cmm/m/it/12 Fromeq:\OT1/cmr/m/n/12 (3) \OMS/cmsy/m/n/12 ^^@
 []


Overfull \hbox (52.1614pt too wide) in paragraph at lines 266--295
\OML/cmm/m/it/12 eq:\OT1/cmr/m/n/12 (7)\OML/cmm/m/it/12 ; wefinallygetsmoothfunctionofgraphmodelas \OT1/cmr/m/n/12 : [] [][] =
 []


Overfull \hbox (146.70966pt too wide) in paragraph at lines 266--295
\OML/cmm/m/it/12 p\OT1/cmr/m/n/12 ([][]) [] [][]\OML/cmm/m/it/12 :\OT1/cmr/m/n/12 (13)\OML/cmm/m/it/12 InKFM; ingeneral; wehave \OT1/cmr/m/n/12 :
 []


Overfull \hbox (10.69922pt too wide) in paragraph at lines 266--295
\OML/cmm/m/it/12 p [] \OT1/cmr/m/n/12 = \OMS/cmsy/m/n/12 N\OT1/cmr/m/n/12 (\OML/cmm/m/it/12 A\OT1/cmr/bx/n/12 x[] \OT1/cmr/m/n/12 + \OML/cmm/m/it/12 Bu[]; Q\OT1/cmr/m/n/12 )\OML/cmm/m/it/12 ; \OT1/cmr/m/n/12 (14)\OML/cmm/m/it/12 whichAisstatetransformmatrix; and \OT1/cmr/m/n/12 :
 []


Overfull \hbox (320.10936pt too wide) in paragraph at lines 266--295
\OML/cmm/m/it/12 p [] \OT1/cmr/m/n/12 = \OMS/cmsy/m/n/12 N\OT1/cmr/m/n/12 (\OML/cmm/m/it/12 H\OT1/cmr/bx/n/12 x[]\OML/cmm/m/it/12 ; R\OT1/cmr/m/n/12 )\OML/cmm/m/it/12 ; \OT1/cmr/m/n/12 (15)\OML/cmm/m/it/12 whichisobservationmatrix; and^^Y[] \OT1/cmr/m/n/12 = \OML/cmm/m/it/12 p\OT1/cmr/m/n/12 (\OT1/cmr/bx/n/12 x[]\OT1/cmr/m/n/12 )\OML/cmm/m/it/12 ; whichisinitialstatematrix:Thus; KFMsystemparameteris\OT1/cmr/m/n/12 
 =
 []


Overfull \hbox (0.50838pt too wide) in paragraph at lines 266--295
[]\OML/cmm/m/it/12 :Letsmoothfunctionofgraphmodel\OT1/cmr/m/n/12 (\OML/cmm/m/it/12 eq:\OT1/cmr/m/n/12 13)\OML/cmm/m/it/12 bef[]; wehave \OT1/cmr/m/n/12 :
 []

[6]

Package natbib Warning: Citation `yu2023signavatars' on page 7 undefined on input line 377.

[7] [8] [9]

Package natbib Warning: There were undefined citations.

[10] [11 <./Figure_1.pdf>] [12 <./Figure_2.pdf>] (./elsarticle-template-num.aux

Package natbib Warning: Citation(s) may have changed.
(natbib)                Rerun to get citations correct.

)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 4416 strings out of 476065
 65747 string characters out of 5792787
 1946190 words of memory out of 5000000
 26463 multiletter control sequences out of 15000+600000
 569193 words of font info for 78 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 72i,13n,79p,1275b,268s stack positions out of 10000i,1000n,20000p,200000b,200000s
<d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr17.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmti10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmti12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmti7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1200.pfb>
Output written on elsarticle-template-num.pdf (14 pages, 1100126 bytes).
PDF statistics:
 223 PDF objects out of 1000 (max. 8388607)
 132 compressed objects within 2 object streams
 0 named destinations out of 1000 (max. 500000)
 11 words of extra memory for PDF output out of 10000 (max. 10000000)

