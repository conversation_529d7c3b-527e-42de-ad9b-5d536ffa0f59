%% 
%% Copyright 2007-2025 Elsevier Ltd
%% 
%% This file is part of the 'Elsarticle Bundle'.
%% ---------------------------------------------
%% 
%% It may be distributed under the conditions of the LaTeX Project Public
%% License, either version 1.3 of this license or (at your option) any
%% later version.  The latest version of this license is in
%%    http://www.latex-project.org/lppl.txt
%% and version 1.3 or later is part of all distributions of LaTeX
%% version 1999/12/01 or later.
%% 
%% The list of all files belonging to the 'Elsarticle Bundle' is
%% given in the file `manifest.txt'.
%% 
%% Template article for Elsevier's document class `elsarticle'
%% with numbered style bibliographic references
%% SP 2008/03/01
%% $Id: elsarticle-template-num.tex 272 2025-01-09 17:36:26Z rishi $
%%
\documentclass[preprint,12pt]{elsarticle}

%% Use the option review to obtain double line spacing
%% \documentclass[authoryear,preprint,review,12pt]{elsarticle}

%% Use the options 1p,twocolumn; 3p; 3p,twocolumn; 5p; or 5p,twocolumn
%% for a journal layout:
%% \documentclass[final,1p,times]{elsarticle}
%% \documentclass[final,1p,times,twocolumn]{elsarticle}
%% \documentclass[final,3p,times]{elsarticle}
%% \documentclass[final,3p,times,twocolumn]{elsarticle}
%% \documentclass[final,5p,times]{elsarticle}
%% \documentclass[final,5p,times,twocolumn]{elsarticle}

%% For including figures, graphicx.sty has been loaded in
%% elsarticle.cls. If you prefer to use the old commands
%% please give \usepackage{epsfig}

%% The amssymb package provides various useful mathematical symbols
\usepackage{amssymb}
%% The amsmath package provides various useful equation environments.
\usepackage{amsmath}
%% The amsthm package provides extended theorem environments
%% \usepackage{amsthm}

%% The lineno packages adds line numbers. Start line numbering with
%% \begin{linenumbers}, end it with \end{linenumbers}. Or switch it on
%% for the whole article with \linenumbers.
%% \usepackage{lineno}

\journal{Nuclear Physics B}

\begin{document}

\begin{frontmatter}

%% Title, authors and addresses

%% use the tnoteref command within \title for footnotes;
%% use the tnotetext command for theassociated footnote;
%% use the fnref command within \author or \affiliation for footnotes;
%% use the fntext command for theassociated footnote;
%% use the corref command within \author for corresponding author footnotes;
%% use the cortext command for theassociated footnote;
%% use the ead command for the email address,
%% and the form \ead[url] for the home page:
%% \title{Title\tnoteref{label1}}
%% \tnotetext[label1]{}
%% \author{Name\corref{cor1}\fnref{label2}}
%% \ead{email address}
%% \ead[url]{home page}
%% \fntext[label2]{}
%% \cortext[cor1]{}
%% \affiliation{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}
%% \fntext[label3]{}

\title{$\Phi$KFM: Physically-Constrained Sign Language Generation via Attention-LSTM and Differentiable Kalman Filtering}

%% use optional labels to link authors explicitly to addresses:
%% \author[label1,label2]{}
%% \affiliation[label1]{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}
%%
%% \affiliation[label2]{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}

\author[label1]{Qinkun Xiao} %% Author name
\author[label2]{Peiran Liu}
\author[label2]{Lu Li}
\author{Jielei Xiao}
%% Author affiliation
\affiliation[label1]{organization={Department of Electrical and Information Engineering, Xi'an Technological University},%Department and Organization
            addressline={No.2 Xuefu Middle Road}, 
            city={Xi'an},
            postcode={}, 
            state={Shaanxi},
            country={China}}

%% Author affiliation
\affiliation[label2]{organization={Department of Mechanical and Electrical Engineering, Xi'an Technological University},%Department and Organization
            addressline={No.2 Xuefu Middle Road}, 
            city={Xi'an},
            postcode={}, 
            state={Shaanxi},
            country={China}}
                

%% Abstract
\begin{abstract}
We present $\Phi$KFM, a novel sign language generation (SLG) framework that combines an attention-enhanced with differentiable Kalman filtering model (KFM) to address linguistic fidelity, biomechanical compliance, and spatiotemporal coherence. $\Phi$KFM  explicitly integrates physical constraints through parallel LSTM streams for kinematic synchronization and probabilistic filtering, eliminating the need for post-processing. Our composite loss function, balancing KL divergence and biomechanical penalties, achieves a joint compliance rate of 91.8\% (a 3.7\% improvement) and reduces acceleration discontinuities by 62.7\%. Experimental results demonstrate superior motion naturalness (MOS: 4.7/5.0) with only a 14ms computational overhead. Key innovations include: 1) physics-aware attention for motion-text alignment, 2) differentiable filtering to enforce anatomical constraints, and 3) adaptive curriculum learning. $\Phi$KFM advances assistive communication and establishes new standards for physically-constrained motion generation.
\end{abstract}

%%Graphical abstract
\begin{graphicalabstract}
%\includegraphics{grabs}
\end{graphicalabstract}

%%Research highlights
\begin{highlights}
\item Research highlight 1
\item Research highlight 2
\end{highlights}

%% Keywords
\begin{keyword}
SLG \sep KFM \sep biomechanical compliance \sep LSTM


\end{keyword}

\end{frontmatter}

%% Add \usepackage{lineno} before \begin{document} and uncomment 
%% following line to enable line numbers
%% \linenumbers

%% main text
%%

%% Use \section commands to start a section
\section{Introduction}
\label{sec1}
%% Labels are used to cross-reference an item using \ref command.
As the inverse process of sign language recognition (SLR), sign language generation (SLG) aims to translate linguistic content into semantically accurate and biomechanically plausible sign sequences \cite{ref1}. Despite substantial progress in SLR, where modern approaches achieve recognition accuracies exceeding 92\% in controlled environments \cite{ref2}, SLG still faces persistent challenges in linguistics, biomechanics, and computer science \cite{ref3}.

An effective SLG system must simultaneously address three interconnected challenges. First, linguistic fidelity must be preserved, since sign languages possess unique grammar and syntax that differ significantly from spoken languages \cite{ref4}. These complex spatial-kinematic systems involve the precise coordination of handshapes, movements, spatial locations, and facial expressions \cite{ref5}. Early rule-based systems employed finite state machines and kinematic interpolation \cite{ref6} but lacked flexibility to accommodate natural variability \cite{ref7}. Although modern deep learning techniques have improved semantic representation \cite{ref8}, maintaining grammatical accuracy while capturing nuanced natural variations remains a significant challenge \cite{ref9}.

Additionally, biomechanical compliance is essential due to strict physiological constraints often violated by current generative models \cite{ref10}. These constraints encompass joint angle ranges (e.g., finger joints typically 0°-90°) \cite{ref11}, velocity thresholds (e.g., elbow velocities below 1.5 m/s) \cite{ref12} , characteristic acceleration patterns (e.g., damped harmonic motion of the wrist) \cite{ref13}, and principles of energy minimization \cite{ref14}. According to the ASL-Physics 2023 benchmark, approximately 34\% of generated joint angles exceed biomechanical limits \cite{ref15}, resulting in unnatural or physically implausible movements \cite{ref16}, which significantly undermines communicative clarity \cite{ref17}.

Furthermore, achieving spatiotemporal coherence—which entails precise temporal coordination and smooth motion transitions—remains a major challenge \cite{ref18}. Current systems frequently produce discontinuous motion, with a discontinuity rate 58\% higher than that of human signers, as observed in the SignGen-2023 dataset \cite{ref19}. This issue stems from insufficient modeling of inter-articular dependencies \cite{ref20}, limited phrase-level contextual windows \cite{ref21}, and inconsistencies between training and inference conditions \cite{ref22}. These discontinuities hinder both the naturalness and intelligibility of the generated motion, as rhythmic features of signing convey essential linguistic cues \cite{ref23}.

SLG methodologies have undergone significant evolution over the past decades \cite{ref24}. Early rule-based approaches relied heavily on finite state machines, handcrafted databases, and kinematic interpolation techniques \cite{ref25}, offering precise control yet lacking flexibility \cite{ref26}. Statistical learning techniques (2015-2020), including Hidden Markov Models \cite{ref27}, Dynamic Time Warping \cite{ref28}, and Gaussian Mixture Models \cite{ref29}, improved generalizability but were inadequate in handling the high dimensionality and complexity of sign language data. Recent deep learning approaches include LSTM architectures effective in temporal modeling yet prone to error accumulation, Transformer-based models that handle long-range dependencies but incur higher computational complexity, and generative models like GANs, VAEs, and diffusion models, which often produce physically implausible motions.

Specialized techniques for enforcing biomechanical constraints—such as post-processing filters, Lagrangian neural networks, and physics-inspired loss functions—have shown promise in improving motion plausibility. However, they often introduce computational latency and demand extensive parameter tuning. Moreover, these methods typically regard physical constraints as auxiliary components, rather than integrating them into the core generation process.

In response, this paper introduces $\Phi$KFM, a novel SLG framework that fundamentally rethinks the integration of physical constraints into the generative architecture. $\Phi$KFM combines an attention-enhanced LSTM with a differentiable Kalman filtering model to simultaneously address linguistic fidelity, biomechanical compliance, and spatiotemporal coherence. The framework employs a dual-stream synchronization mechanism that jointly models joint coordinates and kinematic states, while probabilistic filtering ensures real-time enforcement of physiological constraints. Additionally, a composite loss function—blending KL divergence, biomechanical penalties, and coherence metrics—guides training under an adaptive curriculum, offering both efficiency and robustness.

Implementation advantages include exceptional computational efficiency with minimal overhead (14ms per frame) and robust generalization across CSL500, WLASL, and Kinetics datasets, demonstrating improvements in joint compliance (91.8\%, +3.7\% over baselines), reduced acceleration discontinuities (62.7\%), and temporal naturalness (mean opinion scores of 4.7/5.0). Qualitative assessments further confirm enhancements in elbow trajectory realism, finger coordination, and phrase-level rhythm.

Beyond technical contributions, $\Phi$KFM supports accessible communication technologies, improves SL avatars, and enhances educational applications. It also opens new research pathways in physics-informed generative models and real-time constrained optimization, advancing telepresence, digital interpretation, and VR/AR platforms. This work marks a significant paradigm shift by placing biomechanical constraints at the core of the generative process, demonstrating notable improvements in both objective metrics and subjective naturalness assessments. Future developments will explore extending $\Phi$KFM to full-body signing and interactive systems, further promoting accessible communication technologies.


%% Use \subsection commands to start a subsection.
\section{Methods}
\subsection{Overview}
\label{subsec1}

The overall framework of proposed method is illustrated in Fig.\ref{fig1} The main novelty of our approach is to combine deep neural network (DNN) sequence generation models and probabilistic graphical models (PGM) for generating SL obeyed real-world physical motion laws. The proposed model contains two main components: the DNN and the PGM, we call the fused model $\Phi$KFM. The DNN includes encoder, attention, and decoder modules. In DNN, the input is a text sequence $\mathbf{S}=(s_1,\cdots,s_n)$, and the decoder outputs an initial estimated SL skeleton sequence $\hat{\mathbf{K}}=({\hat{k}}_1,\cdots,{\hat{k}}_T)$. In PGM, the Kalman filtering model (KFM) is used for skeleton data updating, yielding an updated skeleton sequence $\mathbf{X}=(\mathbf{x}_1,\cdots,\mathbf{x}_T)$, that conforms to real-world physical motion laws. The loss function $\mathcal{L}$ is the distribution distance between the true skeleton sequence $\mathbf{K}^{real}$ corresponding to the text $\mathbf{S}$ and the optimal generated skeleton sequence $\mathbf{X}$. In system learning, we consider the loss function with physical constraints ($\mathbf{U}$) as the optimization objective.

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_1.pdf}
\caption{The proposed DNN-PGM SLG framework ($Phi$KFM). (a) The training of $Phi$KFM. In the part of DNN, includes 3 modules: the encoder $(f_e)$, the attention module $(f_{att})$, the decoder $(f_d)$. In the part of PGM, we use a KFM, which is a probability graph model, to optimize the initial generated skeleton. The system can generate optimal skeleton sequence $\mathbf{X}$ obeyed the real-world physical motion laws. The loss function $\mathcal{L}$ is the combination of KL distance $(\mathcal{L}_{KL})$ and physical constraints $(\mathcal{L}_{phy})$ given by inputted control $\mathbf{U}$. (b) The testing of model. To build a SL discriminator for testing the classification accuracy of generated SL.}\label{fig1}
\end{figure}

\subsection{Module Design}
\subsubsection{Deep Network}
The first and basic component is encoder ($f_e$), as shown in  Fig.\ref{fig1}(a). The module encodes the text sequence $\mathbf{S}=(s_1,\cdots,s_T)$ into the feature sequence $\mathbf{h}=(h_1,\cdots,h_T)$. We use a BiLSTM network as the encoder, and our model can effectively represent the context information. When $\mathbf{S} $ is fed into encoder $f_e$, we have:

\begin{equation}\label{eq1}
\left(h_1,\cdots,h_T\right)=f_e(\mathbf{S},h_0;\theta_e)
\end{equation}

where $h_0$ is the initial given data, $\theta_e$ is the encoder parameter, and $h_T$ is feature that contains the global information of $\mathbf{S}$. 
The attention module $f_{att}$ calculates the attention coefficient $\alpha_{ti}$ and the context vector $cv_t$ of the input skeleton sequence. Let $h_i$ be a feature of $k_i$, we have:

\begin{equation}\label{eq2}
{cv}_t=\sum_{i=1}^{T}{\alpha_{ti}h_i}
\end{equation}

The decoder $f_d$ decodes the $\mathbf{h}$ to generate the skeleton sequence $\hat{\mathbf{K}}$.  To obtain conditional-based generation results. This paper uses a single-layer LSTM network as the decoder. An LSTM-based decoder unit can be represented as:

\begin{equation}\label{eq3}
\left[{\hat{k}}_t,h_t^d\right]=U_d^{LSTM}({\hat{k}}_{t-1},h_{t-1}^d,{cv}_t),                      
\end{equation}

where $U_d^{LSTM}$ is the basic decoder unit of decoder. If $h_0^d=\mathbf{h}$ the calculation of the decoder is: 

\begin{equation}\label{eq4}
\hat{\mathbf{K}}=\left({\hat{k}}_1,\cdots,{\hat{k}}_T\right)=f_d\left(\mathbf{h},\mathbf{CV};\theta_d\right),        
\end{equation}

where $f_d$ is the decoder, and $\theta_d$ is the decoder parameter, the $\mathbf{CV}=({cv}_1,\cdots,{cv}_T)$, $\hat{\mathbf{K}}$ is the generated skeleton sequence. 

\subsubsection{Dynamic Bayesian Network}
In our system, we use a kind of dynamic Bayesian network (DBN) model to update the generated skeleton data $(\hat{\mathbf{K}})$, which is called as KFM. In KFM, observation sequence is the $\hat{\mathbf{K}}=\{\hat{k}_t\}_{t=1}^T$ (produced by DNN), SL physical constraint sequence is $\mathbf{U}=\{u_t\}_{t=1}^T$, state sequence is $\mathbf{X}=\{\mathbf{x}_t\}_{t=1}^T$. The $\hat{k}_t=(J_{t,i})_{i=1}^n$ is estimated value of generated skeleton data in time $t$ $(1<t<T)$, and where the $J_{t,i}$ is the coordinate value of $i$-th joint in time $t$. The $\mathbf{x}_t$ is state value of filter, which related to the motion state of skeleton, such as velocity and acceleration of joints in skeleton, hence, the $\mathbf{x}_t$ can be written as:

\begin{equation}\label{eq5}
\mathbf{x}_t=[(J_{t,i})_{i=1}^n,({\dot{J}}_{t,i})_{i=1}^n,({\ddot{J}}_{t,i})_{i=1}^n]T,                       
\end{equation}

where ${\dot{J}}_{t,i}$ and ${\ddot{J}}_{t,i}$ are velocity and acceleration of $i-th$ joint in time t. The $u_t$ is the physical constraint in time t, then physical constraint is: (1) $J_{t,i}-J_{t-1,i}<\varepsilon_1$, and (2) $J_{t,i}-J_{t-1,i}<\varepsilon_2$, (3) $\theta_{min}\le\theta_{t,i}\le\theta_{max}$, as shown in ~Fig.\ref{fig2}. In order to integrate discrete inequality constraints into a continuous KFM probabilistic framework, we have:

\begin{equation}\label{eq6}
u_t=\begin{pmatrix}
\{\max{J_{t,i}-J_{t-1,i}-\varepsilon_1,0}\}_{i=1}^n \\
\{\max{J_{t,i}-J_{t-1,i}-\varepsilon_2,0}\}_{i=1}^n \\
\{\mathbb{I} (\theta_{t,i}\notin[\theta_{\min},\theta_{\max}])\}_{i=1}^n
\end{pmatrix}.
\end{equation}

The filtering calculation is key step for skeleton data updating. The details are described as follow. Firstly, the filtering calculation is a recurrent calculation: 

\begin{equation}\label{eq7}
p\left({\mathbf{x}_{t+1}|\hat{k}}_{1:t+1},u_{1:t+1}\right)=f({\hat{k}}_{t+1},p\left({\mathbf{x}_t|\hat{k}}_{1:t},u_{1:t}\right)),       
\end{equation}

where $f(.)$ is filter function, and ${\hat{k}}_t=(J_{t,i})_{i=1}^n$ is estimated value of generated skeleton data in time t$ (1<t<T)$, which can be seen as the observed data of filter.
According to Bayes rule and based on Eqs.\ref{eq5}, we have:

\begin{align}\label{eq8}
p\left({\mathbf{x}_{t+1}|\hat{k}}_{1:t+1},u_{1:t+1}\right)
&=p\left({\mathbf{x}_{t+1}|\hat{k}}_{t+1},u_{t+1},{\hat{k}}_{1:t},u_{1:t}\right) \nonumber \\
&=\alpha p\left({\hat{k}}_{t+1},u_{t+1}\middle|\mathbf{x}_{t+1},{\hat{k}}_{1:t},u_{1:t}\right)p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right) \nonumber \\
&=\alpha p\left({\hat{k}}_{t+1},u_{t+1}\middle|\mathbf{x}_{t+1}\right)p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right)
\end{align}

where $p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right)$ can be written as:

\begin{equation}\label{eq9}
p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right)=\int{p{(\mathbf{x}}_{t+1}\left|\mathbf{x}_t\right)p(\mathbf{x}_t|{\hat{k}}_{1:t},u_{1:t})}      
\end{equation}

Hence, we get the filtering function as:

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_2.pdf}
\caption{The physical constraint of SL in time $t$.}\label{fig2}
\end{figure}

\begin{align}\label{eq10}
p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t+1},u_{1:t+1}\right)
&=\alpha p\left({\hat{k}}_{t+1},u_{t+1}\middle|\mathbf{x}_{t+1}\right)p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right) \nonumber \\
&=\alpha p\left({\hat{k}}_{t+1},u_{t+1}\middle|\mathbf{x}_{t+1}\right)\int{p\left(\mathbf{x}_{t+1}\middle|\mathbf{x}_t\right)p(\mathbf{x}_t|{\hat{k}}_{1:t},u_{1:t})d\mathbf{x}_t}
\end{align}

The next, based on the filtering function, we can get smooth function of graph model. For at time $j$ ($1<j<t$), we have:

\begin{align}\label{eq11}
p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:t+1},u_{1:t+1}\right)
&=p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:j},u_{1:j},{\hat{k}}_{j+1:t},u_{j+1:t}\right) \nonumber \\
&=\alpha p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:j},u_{1:j}\right)p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j,{\hat{k}}_{1:j},u_{1:j}\right) \nonumber \\
&=\alpha p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:j},u_{1:j}\right)p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j\right)
\end{align}

where $p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:j},u_{1:j}\right)$ is filtering part, which can be processed by Eqs.\ref{eq5}, and $p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j\right)$ is calculated by:

\begin{align}\label{eq12}
p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j\right)
&=\int{p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j,\mathbf{x}_{j+1}\right)p\left(\mathbf{x}_{j+1}\middle|\mathbf{x}_j\right)}d\mathbf{x}_{j+1} \nonumber \\
&=\int{p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_{j+1}\right)p\left(\mathbf{x}_{j+1}\middle|\mathbf{x}_j\right)}d\mathbf{x}_{j+1} \nonumber \\
&=\int{p\left({\hat{k}}_{j+1:t},u_{j+1:t},{\hat{k}}_{j+2:t},u_{j+2:t}\middle|\mathbf{x}_{j+1}\right)p\left(\mathbf{x}_{j+1}\middle|\mathbf{x}_j\right)d\mathbf{x}_{j+1}} \nonumber \\
&=\int{p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_{j+1}\right)p({\hat{k}}_{j+2:t},u_{j+2:t}|\mathbf{x}_{j+1})p\left(\mathbf{x}_{j+1}\middle|\mathbf{x}_j\right)d\mathbf{x}_{j+1}}
\end{align}

From Eqs.\ref{eq3}-Eqs.\ref{eq7}, we finally get smooth function of graph model as:

\begin{align}\label{eq13}
\max_{\mathbf{x}_{1:T-1}}{p\left(\mathbf{x}_{1:T}\middle|{\hat{k}}_{1:T},u_{1:T}\right)}
&=\alpha p\left({\hat{k}}_T,u_T\middle|\mathbf{x}_T\right)\max_{\mathbf{x}_{T-1}}{p\left(\mathbf{x}_T\middle|\mathbf{x}_{T-1}\right)} \nonumber \\
&\quad \times \max_{\mathbf{x}_{1:T-2}}{p\left(\mathbf{x}_{1:T-1}\middle|{\hat{k}}_{1:T-1},u_{1:T-1}\right)}
\end{align}

In KFM, in general, we have:
\begin{equation}\label{eq14}
p\left(\mathbf{x}_t\middle|\mathbf{x}_{t-1}\right)=\mathcal{N}(A\mathbf{x}_{t-1}+Bu_{t-1},Q)
\end{equation}

which $A$ is state transform matrix, and:
\begin{equation}\label{eq15}
p\left({\hat{k}}_t,u_t\middle|\mathbf{x}_t\right)=\mathcal{N}(H\mathbf{x}_t,R)
\end{equation}

which is observation matrix, and $\pi_0=p(\mathbf{x}_0)$, which is initial state matrix. Thus, KFM system parameter is $\Omega=\{\pi_0,A,B,H,Q,R\}$. Let smooth function of graph model (eq.13) be $f_{KFM}$, we have:

\begin{equation}\label{eq16}
\mathbf{X}\gets f_{KFM}(\hat{\mathbf{K}},\mathbf{U};\Omega)
\end{equation}

\subsection{System Solution and Robustness Analysis}
We have described the framework of the system above, and in $\Phi$KFM system, two basic points should be considered. One is whether there is an optimized solution to meet our diverse SLG needs under the proposed $\Phi$KFM model, and the other is whether the $\Phi$KFM system is continuously derivable and robustness. If these properties are satisfied, we have reason to believe that the proposed model framework is domain universal and can be applied to other sequence generation domains in addition to SLG. Therefore, below we will first discuss two theorems, one is the existence theorem of the optimized solution based on this model, and the other is the continuous derivability and robustness of system.

\textbf{Theorem 1:} Let $\mathcal{X}\in\mathbb{R}^n$ denote the feasible set of produced sequences, $\mathbf{K}^{real}$ is real sequence data, $\mathbf{X}$ is estimated state of sequence data, $\mathbf{S}$ is the input text sequence, $\mathbf{U}$ is physical constraint of produced sequence, and the $\Phi$KFM-based sequence production optimization is equivalent to solve the constrained convex optimization:
\begin{equation}\label{eq17}
J(\mathbf{X})=\lambda_1D_{KL}\left(p_{real}\left(\mathbf{K}^{real}|\mathbf{S}\right)\parallel q_\Phi\left(\mathbf{X}|\mathbf{S}\right)\right)+\lambda_2\mathbf{U}
\end{equation}

where $D_{KL}$ is the Kullback-Leibler divergence, $\sum_{i}{\lambda_i=1}$, and $p_{real}$ is real skeleton sequence distribution, the $q_\Phi$ is generated skeleton sequence, the $\Phi=\{\theta_e,\theta_d,\Omega\}$. Under the $\Phi$KFM framework, the generated sequence $\mathbf{X}^\ast$ is an optimal solution of $J(\mathbf{X})$, i.e.,

\begin{equation}\label{eq18}
\mathbf{X}^\ast = \arg\min_{\mathbf{X}\in\mathcal{X}} J(\mathbf{X})
\end{equation}

\textbf{Proof:} Based on the provided the $\Phi$KFM coupling model, the definition of feasible set:

\begin{equation}\label{eq19}
\mathcal{X}=\left\{\mathbf{X}\mid\theta_{min}\le\theta_{t,m}\le\theta_{max},\forall t,m\right\}\subset\mathbb{R}^n
\end{equation}

where $n = 3\times M\times T$ represents the skeleton sequence dimension, $M$ is the number of joints, and $\theta_{t,m}$ denotes the joint angle of the $m$-th joint at frame $t$.

(1) Convexity of Objective Function $J(\mathbf{X})$

Firstly, convexity of $\mathcal{X}$ can be discussed. For any $\mathbf{X}^{(1)},\mathbf{X}^{(2)}\in\mathcal{X}$ and $\alpha\in[0,1]$, the linear combination: $\mathbf{X}^{(\lambda)}=\alpha\mathbf{X}^{(1)}+\left(1-\alpha\right)\mathbf{X}^{(2)}$ satisfies:

\begin{equation}\label{eq20}
\theta_{min}\le\alpha\theta_{t,j}^{(1)}+(1-\alpha)\theta_{t,j}^{(2)}\le\theta_{max},\forall t,j
\end{equation}

Since $\theta_{min}$ and $\theta_{max}$ are constants, the inequality remains closed under convex combinations. Velocity and acceleration constraints are affine transformations of joint angles. The intersection of convex sets preserves convexity. Thus, $\mathcal{X}$ is a convex set.

Secondly, to decomposed of objective function Eqs.\ref{eq17}, we know, the $q_\Phi$ is distribution from DNN-PGM. When $p_{real}$ is fixed and $q_\Phi$ is an exponential family distribution (e.g., Gaussian), the KL divergence is convex in the parameters of $p_{real}$. The next, in Eqs.\ref{eq6}, the $\lambda\mathbf{U}$ is a quadratic function, which is strictly convex. Overall, the non-negative linear combination of convex functions preserves convexity. Hence, $J(\mathbf{X})$ is convex over $\mathcal{X}$.

(2) Existence of optimal solution ($\mathbf{X}^\ast$)

$\mathcal{X}$ is closed (due to inequality constraints) and bounded (joint angles are bounded), making it compact. $J(\mathbf{X})$ is continuous within $\mathcal{X}$. By the Weierstrass Extreme Value Theorem, a lower semi-continuous function on a compact set attains its minimum. Hence, there is a $\mathbf{X}^\ast\in\mathcal{X}$ to make Eqs.\ref{eq18} hold.

Completing the proof.

\textbf{Theorem 2:} the $\Phi$KFM sequence production model robustness satisfies the following conditions:

(1) Let the $f_{DNN}$ be DNN sequence generator, then, Lipschitz continuity of the $f_{DNN}$ should be satisfied:

\begin{equation}\label{eq21}
\exists L>0,\forall s,\Delta s, \|f_{DNN}(s+\Delta s)-f_{DNN}(s)\|\le L\|\Delta s\|
\end{equation}

where $L$ is the Lipschitz constant determined by the network weights $W$ and activation functions $\sigma(\cdot)$, the $s$ is the input of $f_{DNN}$.

(2) In PGM, let error be $e_t=\mathbf{x}_t-{\hat{\mathbf{x}}}_t$, and covariance is $P_t=\mathbb{E}[ee^T]$, the posterior covariance matrix $P_t$ satisfies:
\begin{equation}\label{eq22}
\text{tr}(P_t)\le \text{tr}(P_{t-1})
\end{equation}

\textbf{Proof:}

(1) Lipschitz continuity of the DNN Generator.

Let the $f_{DNN}$ be parameterized by weight matrices $W$. The ReLU function $\sigma(\cdot)$ has a gradient satisfying: $\|\nabla\sigma(\cdot)\|\le1$, giving it a Lipschitz constant of 1. This means ReLU does not amplify input perturbations. If the operator norm is defined as:

\begin{equation}\label{eq23}
\|W\|_{op}=\sup_{x\neq0}\frac{\|Wx\|}{\|x\|}
\end{equation}

which quantifies the maximum "amplification factor" of the matrix on input perturbations. For example, if $\|W\|_{op}=2$, an input perturbation $\nabla x$ is amplified at most by a factor of 2 after passing through $W$.

Assume the $f_{DNN}$ has $N$ layers, each with weight matrix $W_i$ and ReLU activation $\sigma(\cdot)$, we have:
\begin{equation}
f_{DNN}=f_{DNN}^N\circ f_{DNN}^{N-1}\circ\cdots\circ f_{DNN}^1
\end{equation}

For $i$-th layer, $f_{DNN}^i(z)=W_i\cdot\sigma(z)$, according to chain rule, the input gradient is: $\nabla_sf_{DNN}(s)=J_N\cdot J_{N-1}\cdots J_1$, where $J_k$ is Jacobi Matrix, can be defined as: $J_k=\nabla_{z_{k-1}}f_{DNN}(z_{k-1})$, the operator norm of $J_k$: $\|J_k\|_{op}\le\|W_k\|_{op}$, hence:
\begin{equation}
\|\nabla_sf_{DNN}(s)\|_{op}\le\prod_{k=1}^{N}\|W_k\|_{op}=L
\end{equation}

where $L$ is Lipschitz constant. For proposed SLG model, we have:
\begin{align}
f_{DNN}(s+\Delta s)-f_{DNN}(s) &= \int{\nabla_sf_{DNN}(s+\Delta s)\cdot\Delta s \, dt} \nonumber \\
&\le \|\nabla_sf_{DNN}(s+\Delta s)\|_{op}\cdot\|\Delta s\| \, dt
\end{align}

Because $\|\nabla_sf_{DNN}(s+\Delta s)\|_{op}\le L$, thus, $\forall s\in\mathcal{S}$:
\begin{equation}
\|f_{DNN}(s+\Delta s)-f_{DNN}(s)\|\le L\cdot\|\Delta s\|
\end{equation}
(2) Noise Robustness via PGM

The physics equation of PGM is:
\begin{align}
\mathbf{x}_t &= A\mathbf{x}_{t-1}+Bu_{t-1}+w_{t-1} \nonumber \\
{\hat{k}}_t &= H\mathbf{x}_t+v_t
\end{align}

where $A$, $B$ and $H$ are state matrix, control matrix, and observation matrix, respectively. The $w_{t-1}\sim\mathcal{N}(0,Q)$, $v_t\sim\mathcal{N}(0,R)$, where $Q,R\succ0$ are the state noise covariance matrix and observation noise covariance matrix, respectively. Based on Woodbury inequality [10], we have:
\begin{equation}
P_t=\left(\left(AP_{t-1}A^T+Q\right)^{-1}+H^TR^{-1}H\right)^{-1}
\end{equation}

Let $Y_{t-1}=P_{t-1}^{-1}$, then:
\begin{equation}
Y_t=A^{-T}Y_{t-1}A^{-1}+Q^{-1}+H^TR^{-1}H
\end{equation}

Due to $H^TR^{-1}H+Q^{-1}>0$, thus: $Y_t>A^{-T}Y_{t-1}A^{-1}$, and:
\begin{equation}
P_t=Y_t^{-1}<(A^{-T}Y_{t-1}A^{-1})^{-1}=AP_{t-1}A^T
\end{equation}

If $A$ is stability matrix, then:
\begin{equation}
P_t<AP_{t-1}A^T<P_{t-1}\Rightarrow\text{tr}(P_t)<\text{tr}(P_{t-1})
\end{equation}

Completing the proof.

\subsection{System Learning}
Based on total object function J(\mathbf{X}) (eq.(17)), we know, total loss function is \mathcal{L}=\lambda_1\mathcal{L}_{KL}+\lambda_2\mathcal{L}_{phy}, one is distribution loss: \mathcal{L}_{KL}=D_{KL}, the other is physical constraint loss:\ \mathcal{L}_{phy}=\mathbf{U}.
For \mathcal{L}_{KL}, the p(\mathbf{K}^{real}) and q_\Phi\left(\mathbf{X}|\mathbf{S}\right) should be as closer as possible. For calculating \mathcal{L}, we assume that the k_t obeys the GMM distribution. For a skeleton contains n joints in time t, J_{t,i}=(x_t^i,y_t^i) represents the position of the ith joint at time t. Assume J_{t,i} obeys a 2-dimensional normal distribution, and J_{t,i}~\mathcal{N}\left(\mathbf{m}_{t,i},\Sigma_{t,i}^{-1}\right), where \mathbf{m}_{t,i} and \Sigma_{t,i}^{-1} are the expectation and variance of J_{t,i}, respectively. Furthermore, we have: k_t~p_{gmm}\left(k_t\right)=\sum_{i=1}^{n}{\pi_i\bullet\mathcal{N}\left(\mathbf{m}_{t,i},\Sigma_{t,i}^{-1}\right)}, where p_{gmm}\left(k_t\right) is a GMM distribution, and \pi_i is the coefficient of the i-th component. Thus, the distribution of skeleton sequence can be:
q_\Phi\left(\mathbf{X}|\mathbf{S}\right)=\prod_{i=1}^{T}{\kappa_i\cdot p_{gmm}(k_t)},                  
   (32)
where \Phi is a parameter of the sequence distribution, and \kappa_i is the skeleton weight of k_t. We can adjust the \Phi using the gradient descent method. 
To enable end-to-end differentiable training, the differentiable f_{KFM} employs a hybrid gradient strategy: the forward pass executes standard Kalman filtering and RTS smoothing algorithms, while the backward propagation combines analytical gradients with automatic differentiation (AD). The forward process strictly follows the iterative computations of classical Kalman filtering (Eqs.7-10) and RTS smoothing (Eqs.11-13), where the filtering phase computes the posterior state distribution via the predict-update cycle (Eq.10), and the smoothing phase optimizes the full-sequence state estimate through backward propagation. Gradient computation adopts a hierarchical strategy: at the micro-level, linear operations including state transition (A\mathbf{x}_{t-1}), observation mapping (H\mathbf{x}_t), and covariance update (AP_{t-1}A⊤+Q) rely on the chain rule of automatic differentiation frameworks. At the macro-level, analytical gradients handle temporal recursive structures (filtering cycle, RTS smoothing). For the smoothing phase, we derive the gradient of RTS iteration based on the implicit function theorem. Physical constraints are embedded through projected gradients:
\nabla_{\mathbf{x}_t}\mathcal{L}_{phy}=\sum_i\mathbb{I}(\theta_{t,i}\notin[\theta_{min},\theta_{max}])⋅∂xt∂θt,i,
(33)
while acceleration continuity constraints △xt3<ε introduce differentiable penalties through Huber loss. This implementation allows f_{KFM} to support gradient backpropagation (\partial\mathcal{L}/\partial\Omega) while preserving the probabilistic semantics of classical Kalman filtering, enabling co-optimization of physical constraints and generation quality as formalized in Theorem 1. Based on above, we write ΦKFM training algorithm as: 


%% Use \subsubsection, \paragraph, \subparagraph commands to 
%% start 3rd, 4th and 5th level sections.
%% Refer following link for more details.
%% https://en.wikibooks.org/wiki/LaTeX/Document_Structure#Sectioning_commands

\subsubsection{Mathematics}
%% Inline mathematics is tagged between $ symbols.
This is an example for the symbol $\alpha$ tagged as inline mathematics.

%% Displayed equations can be tagged using various environments. 
%% Single line equations can be tagged using the equation environment.
\begin{equation}
f(x) = (x+a)(x+b)
\end{equation}

%% Unnumbered equations are tagged using starred versions of the environment.
%% amsmath package needs to be loaded for the starred version of equation environment.
\begin{equation*}
f(x) = (x+a)(x+b)
\end{equation*}

%% align or eqnarray environments can be used for multi line equations.
%% & is used to mark alignment points in equations.
%% \\ is used to end a row in a multiline equation.
\begin{align}
 f(x) &= (x+a)(x+b) \\
      &= x^2 + (a+b)x + ab
\end{align}

\begin{eqnarray}
 f(x) &=& (x+a)(x+b) \nonumber\\ %% If equation numbering is not needed for a row use \nonumber.
      &=& x^2 + (a+b)x + ab
\end{eqnarray}

%% Unnumbered versions of align and eqnarray
\begin{align*}
 f(x) &= (x+a)(x+b) \\
      &= x^2 + (a+b)x + ab
\end{align*}

\begin{eqnarray*}
 f(x)&=& (x+a)(x+b) \\
     &=& x^2 + (a+b)x + ab
\end{eqnarray*}

%% Refer following link for more details.
%% https://en.wikibooks.org/wiki/LaTeX/Mathematics
%% https://en.wikibooks.org/wiki/LaTeX/Advanced_Mathematics

%% Use a table environment to create tables.
%% Refer following link for more details.
%% https://en.wikibooks.org/wiki/LaTeX/Tables
\begin{table}[t]%% placement specifier
%% Use tabular environment to tag the tabular data.
%% https://en.wikibooks.org/wiki/LaTeX/Tables#The_tabular_environment
\centering%% For centre alignment of tabular.
\begin{tabular}{l c r}%% Table column specifiers
%% Tabular cells are separated by &
  1 & 2 & 3 \\ %% A tabular row ends with \\
  4 & 5 & 6 \\
  7 & 8 & 9 \\
\end{tabular}
%% Use \caption command for table caption and label.
\caption{Table Caption}\label{tab1}
\end{table}



%% The Appendices part is started with the command \appendix;
%% appendix sections are then done as normal sections
\appendix
\section{Example Appendix Section}
\label{app1}

Appendix text.

%% For citations use: 
%%       \cite{<label>} ==> [1]

%%
Example citation, See \cite{ref1}.

%% If you have bib database file and want bibtex to generate the
%% bibitems, please use
%%
%%  \bibliographystyle{elsarticle-num} 
%%  \bibliography{<your bibdatabase>}

%% else use the following coding to input the bibitems directly in the
%% TeX file.

%% Refer following link for more details about bibliography and citations.
%% https://en.wikibooks.org/wiki/LaTeX/Bibliography_Management

\begin{thebibliography}{99}

%% For numbered reference style
%% \bibitem{label}
%% Text of bibliographic item

\bibitem{ref1}
Yu, Z., Huang, S., Cheng, Y., \& Birdal, T.
\textit{SignAvatars: A Large-scale 3D Sign Language Holistic Motion Dataset and Benchmark}.
arXiv:2310.20436, 2023.

\bibitem{ref2}
Li, Y., Zhang, Y., Zhao, Z., et al.
\textit{CSL: A large-scale Chinese scientific literature dataset}.
arXiv:2209.05034, 2022.

\bibitem{ref3}
Deng, Z., Leng, Y., Chen, J., et al.
\textit{TMS-Net: A multi-feature multi-stream multi-level information sharing network for skeleton-based sign language recognition}.
Neurocomputing, 2024, 572: 127194.

\bibitem{ref4}
Koller, O., Zargaran, S., Ney, H., et al.
\textit{Deep Sign: Enabling Robust Statistical Continuous Sign Language Recognition via Hybrid CNN-HMMs}.
Int J Comput Vis, 2018, 126: 1311–1325.

\bibitem{ref5}
Xie, P., Peng, T., Du, Y., \& Zhang, Q.
\textit{Sign Language Production with Latent Motion Transformer}.
2024 IEEE/CVF Winter Conference on Applications of Computer Vision (WACV), Waikoloa, HI, USA, 2024, pp. 3012-3022.

\bibitem{ref6}
Renuka, D. K., Kumar, L. A., Harini, K. R., et al.
\textit{Sign Language Production Using Generative AI}.
International Conference on Computing and Intelligent Reality Technologies (ICCIRT), Coimbatore, India, 2024, pp. 33-38.

\bibitem{ref7}
Saunders, B., Camgöz, N. C., \& Bowden, R.
\textit{Progressive Transformers for End-to-End Sign Language Production}.
ECCV, 2020, pp. 687-705.

\bibitem{ref8}
Arib, S. H., Akter, R., Rahman, S., \& Rahman, S.
\textit{SignFormer-GCN: Continuous sign language translation using spatio-temporal graph convolutional networks}.
PLoS One, 2025, 20(2): e0316298.

\bibitem{ref9}
Chaudhary, L., Ananthanarayana, T., Hoq, E., et al.
\textit{Signnet ii: A transformer-based two-way sign language translation model}.
IEEE Transactions on Pattern Analysis and Machine Intelligence, 2022, 45(11): 12896-12907.

\bibitem{ref10}
Zhou, B., Chen, Z., Clapés, A., et al.
\textit{Gloss-Free Sign Language Translation: Improving from Visual-Language Pretraining}.
arXiv:2307.14768, 2023.

\bibitem{ref11}
Lin, K., Wang, X., Zhu, L., et al.
\textit{Gloss-Free End-to-End Sign Language Translation}.
arXiv:2305.12876, 2023.

\bibitem{ref12}
Yang, X., Lim, Z., Jung, H., et al.
\textit{Estimation of Finite Finger Joint Centers of Rotation Using 3D Hand Skeleton Motions Reconstructed from CT Scans}.
Appl. Sci., 2020, 10: 9129.

\bibitem{ref13}
Qazi, A., et al.
\textit{ExerAIde: AI-assisted Multimodal Diagnosis for Enhanced Sports Performance and Personalised Rehabilitation}.
In CVPR Workshops, 2024.

\bibitem{ref14}
Sartinas, E. G., Psarakis, E. Z., \& Kosmopoulos, D. I.
\textit{Motion-based sign language video summarization using curvature and torsion}.
arXiv:2305.16801, 2023.

\bibitem{ref15}
Zhang, H., Goodfellow, I., Metaxas, D., et al.
\textit{StyleSwin: Transformer-Based GAN for High-Resolution Image Generation}.
In CVPR, 2022.

\bibitem{ref16}
Chen, L., Xu, Y., Zhu, Q.-X., \& He, Y.-L.
\textit{Adaptive Multi-Head Self-Attention Based Supervised VAE for Industrial Soft Sensing With Missing Data}.
IEEE Transactions on Automation Science and Engineering, 2024, 21(3): 3564-3575.

\bibitem{ref17}
Yuan, W., et al.
\textit{MoGenTS: Motion Generation based on Spatial-Temporal Joint Modeling}.
In NeurIPS, 2024.

\bibitem{ref18}
Shlezinger, N., et al.
\textit{AI-Aided Kalman Filters}.
arXiv:2410.12289, 2025.

\bibitem{ref19}
Cranmer, M., Greydanus, S., Hoyer, S., et al.
\textit{Lagrangian neural networks}.
arXiv:2003.04630, 2020.

\bibitem{ref20}
Jin, X.-B., Chen, W., Ma, H.-J., et al.
\textit{Parameter-Free State Estimation Based on Kalman Filter with Attention Learning for GPS Tracking in Autonomous Driving System}.
Sensors, 2023, 23(20): 8650.

\bibitem{ref21}
Villa-Monedero, M., Gil-Martín, M., Sáez-Trigueros, D., et al.
\textit{Sign Language Dataset for Automatic Motion Generation}.
Journal of Imaging, 2023, 9(12): 262.

\bibitem{ref22}
Feng, S., Li, X., Zhang, S., et al.
\textit{A review: state estimation based on hybrid models of Kalman filter and neural network}.
Systems Science \& Control Engineering, 2023, 11(1): 2173682.

\bibitem{ref23}
Zhang, H., Goodfellow, I., Metaxas, D., \& Odena, A.
\textit{StyleSwin: Transformer-Based GAN for High-Resolution Image Generation}.
In CVPR, 2022.

\bibitem{ref24}
Natarajan, B., \& Elakkiya, R.
\textit{Dynamic GAN for High-Quality Sign Language Video Generation from Skeletal Poses Using Generative Adversarial Networks}.
Soft Computing, 2021, 26(24): 12947–12960.

\bibitem{ref25}
Wang, Y., \& Zhang, H.
\textit{Updated Prediction of Air Quality Based on Kalman-Attention-LSTM Model}.
Sustainability, 2023, 15(1): 356.

\bibitem{ref26}
Yu, Z., Huang, S., Cheng, Y., \& Birdal, T.
\textit{SignAvatars: A Large-scale 3D Sign Language Holistic Motion Dataset and Benchmark}.
arXiv:2310.20436, 2023.

\bibitem{ref27}
Dong, L., Wang, X., \& Nwogu, I.
\textit{Word-Conditioned 3D American Sign Language Motion Generation}.
In Findings of the Association for Computational Linguistics: EMNLP 2024, pp. 9993–9999.

\bibitem{ref28}
Ranum, O., Otterspeer, G., Andersen, J. I., Belleman, R. G., \& Roelofsen, F.
\textit{3D-LEX v1.0: 3D Lexicons for American Sign Language and Sign Language of the Netherlands}.
arXiv:2409.01901, 2024.

\bibitem{ref29}
Saunders, B., Camgoz, N. C., \& Bowden, R.
\textit{Everybody Sign Now: Translating Spoken Language to Photo Realistic Sign Language Video}.
arXiv:2011.09846, 2020.

\end{thebibliography}
\end{document}

\endinput
%%
%% End of file `elsarticle-template-num.tex'.
